/**
  ******************************************************************************
  * @file    dds_wavegen.h
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   DDS波形生成模块 - 竞赛级实现
  *          支持高精度直接数字频率合成，多波形生成
  ******************************************************************************
  * @attention
  * 
  * 本模块特性：
  * 1. 基于查找表的波形生成
  * 2. 多波形支持(正弦波、方波、三角波)
  * 3. 基础频率和幅度控制
  * 4. DAC+DMA+TIM稳定输出
  * 5. 简洁的驱动接口
  * 
  * 硬件连接：
  * - DAC1_OUT: PA4 (模拟输出)
  * - TIM6: 触发源
  * - DMA1_Stream5, Channel 7
  * - 输出频率范围: 0.1Hz - 100kHz
  *
  ******************************************************************************
  */

#ifndef __DDS_WAVEGEN_H
#define __DDS_WAVEGEN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>
#include <math.h>

/* Exported types ------------------------------------------------------------*/

/**
  * @brief  DDS波形类型枚举
  */
typedef enum {
    DDS_WAVE_SINE = 0,           ///< 正弦波
    DDS_WAVE_SQUARE,             ///< 方波
    DDS_WAVE_TRIANGLE,           ///< 三角波
    DDS_WAVE_SAWTOOTH,           ///< 锯齿波
    DDS_WAVE_CUSTOM,             ///< 自定义波形
    DDS_WAVE_COUNT               ///< 波形类型总数
} DDS_WaveType_t;

/**
  * @brief  DDS输出通道枚举
  */
typedef enum {
    DDS_CHANNEL_PRECISION = 0,   ///< 精密段 - DAC8552通道A (1Hz-1kHz)
    DDS_CHANNEL_HIGH,            ///< 高精度段 - DAC8552通道B (1kHz-200kHz)
    DDS_CHANNEL_FAST,            ///< 极速段 - 内置DAC1 (200kHz-3MHz+)
    DDS_CHANNEL_AUTO             ///< 自动选择通道
} DDS_Channel_t;

/**
  * @brief  DDS配置结构体 (扩展三段式架构)
  */
typedef struct {
    uint32_t frequency;          ///< 输出频率(Hz)
    uint16_t amplitude;          ///< 幅度(0-4095, 对应0-3.3V)
    uint16_t offset;             ///< 直流偏移(0-4095)
    uint16_t phase;              ///< 相位偏移(0-3599, 对应0-359.9度)
    DDS_WaveType_t wave_type;    ///< 波形类型
    DDS_Channel_t channel;       ///< 输出通道选择
    uint32_t sample_rate;        ///< DAC采样率(Hz)
    bool enable_modulation;      ///< 调制功能使能
    bool enable_interpolation;   ///< 插值功能使能
    bool enable_auto_switch;     ///< 自动频率切换使能
    bool enable_phase_continuity;///< 相位连续性使能
} DDS_Config_t;

/**
  * @brief  DDS调制配置结构体
  */
typedef struct {
    bool am_enable;              ///< 幅度调制使能
    bool fm_enable;              ///< 频率调制使能
    bool pm_enable;              ///< 相位调制使能
    uint32_t mod_frequency;      ///< 调制频率(Hz)
    uint16_t mod_depth;          ///< 调制深度(0-100%)
    DDS_WaveType_t mod_wave_type; ///< 调制波形类型
} DDS_Modulation_t;

/**
  * @brief  DDS统计信息结构体
  */
typedef struct {
    uint32_t output_samples;     ///< 输出采样数
    uint32_t frequency_changes;  ///< 频率变更次数
    uint32_t wave_changes;       ///< 波形变更次数
    float actual_frequency;      ///< 实际输出频率
    float frequency_error;       ///< 频率误差(%)
    uint16_t max_amplitude;      ///< 最大幅度
    uint16_t min_amplitude;      ///< 最小幅度
    float thd_percent;           ///< 总谐波失真(%)
} DDS_Stats_t;

/**
  * @brief  DDS句柄结构体 (三段式架构扩展)
  */
typedef struct {
    // 硬件实例 (三通道)
    DAC_TypeDef* dac_internal;   ///< 内置DAC实例 (极速段)
    TIM_TypeDef* tim_precision;  ///< 精密段定时器 (TIM7)
    TIM_TypeDef* tim_high;       ///< 高精度段定时器 (TIM6)
    TIM_TypeDef* tim_fast;       ///< 极速段定时器 (TIM2)
    DMA_Stream_TypeDef* dma_fast; ///< 极速段DMA流

    DDS_Config_t config;         ///< 配置参数
    DDS_Modulation_t modulation; ///< 调制参数
    DDS_Stats_t stats;           ///< 统计信息

    // DDS核心参数 (32位兼容模式)
    uint32_t phase_accumulator;  ///< 32位相位累加器 (兼容性优化)
    uint32_t frequency_word;     ///< 32位频率控制字
    uint32_t phase_acc_fast;     ///< 极速段32位相位累加器
    uint32_t freq_word_fast;     ///< 极速段32位频率控制字

    // 三段式波形表
    uint16_t* wave_table_precision; ///< 精密段波形表 (16384点)
    uint16_t* wave_table_high;      ///< 高精度段波形表 (8192点)
    uint16_t* wave_table_fast;      ///< 极速段波形表 (4096点)
    uint16_t* current_wave_table;   ///< 当前波形表指针
    uint32_t wave_table_size;       ///< 当前波形表大小

    // 通道状态管理
    DDS_Channel_t current_channel;  ///< 当前活动通道
    DDS_Channel_t target_channel;   ///< 目标通道 (用于切换)
    uint32_t channel_switch_phase;  ///< 通道切换时的相位

    // 调制参数
    uint32_t mod_phase_acc;      ///< 调制相位累加器
    uint32_t mod_freq_word;      ///< 调制频率控制字

    // 状态标志
    volatile bool is_running;    ///< 运行状态
    volatile bool wave_updated;  ///< 波形更新标志
    volatile bool channel_switching; ///< 通道切换中标志
    volatile bool phase_sync_required; ///< 相位同步需求标志
} DDS_Handle_t;

/* Exported constants --------------------------------------------------------*/

/** @defgroup DDS_Exported_Constants DDS导出常量
  * @{
  */

// 三段式架构波形表大小定义
#define DDS_WAVE_TABLE_SIZE_PRECISION   16384U   ///< 精密段波形表大小 (1Hz-1kHz)
#define DDS_WAVE_TABLE_SIZE_HIGH        8192U    ///< 高精度段波形表大小 (1kHz-200kHz)
#define DDS_WAVE_TABLE_SIZE_FAST        4096U    ///< 极速段波形表大小 (200kHz-3MHz+)
#define DDS_WAVE_TABLE_SIZE             DDS_WAVE_TABLE_SIZE_HIGH  ///< 默认波形表大小

// 相位累加器和精度定义
#define DDS_PHASE_BITS              32U      ///< 32位相位累加器 (兼容性优化)
#define DDS_PHASE_BITS_FAST         32U      ///< 快速模式32位相位累加器
#define DDS_AMPLITUDE_BITS          12U      ///< 幅度位数
#define DDS_DAC_RESOLUTION          4096U    ///< DAC分辨率

// 频率范围定义 (三段式)
#define DDS_MIN_FREQUENCY           1U       ///< 最小频率(1Hz)
#define DDS_MAX_FREQUENCY           3500000U ///< 最大频率(3.5MHz) - 硬件极限
#define DDS_DEFAULT_FREQUENCY       1000U    ///< 默认频率(1kHz)

// 频率分段阈值
#define DDS_FREQ_THRESHOLD_PRECISION    1000U    ///< 精密段上限 (1kHz)
#define DDS_FREQ_THRESHOLD_HIGH         200000U  ///< 高精度段上限 (200kHz)

// 采样率定义 (三段式)
#define DDS_SAMPLE_RATE_PRECISION   100000U  ///< 精密段采样率 (100kHz)
#define DDS_SAMPLE_RATE_HIGH        2000000U ///< 高精度段采样率 (2MHz) - 保持现有
#define DDS_SAMPLE_RATE_FAST        20000000U///< 极速段采样率 (20MHz) - 硬件极限
#define DDS_DEFAULT_SAMPLE_RATE     DDS_SAMPLE_RATE_HIGH ///< 默认采样率

#define DDS_MAX_SAMPLE_RATE         20000000U ///< 最大采样率(20MHz)
#define DDS_MIN_SAMPLE_RATE         100000U  ///< 最小采样率(100kHz)

#define DDS_PHASE_RESOLUTION        3600U    ///< 相位分辨率(0.1度)
#define DDS_AMPLITUDE_MAX           4095U    ///< 最大幅度
#define DDS_OFFSET_MAX              4095U    ///< 最大偏移

// 数学常数
#define DDS_PI                      3.14159265358979323846f
#define DDS_TWO_PI                  (2.0f * DDS_PI)
#define DDS_PHASE_TO_RAD(phase)     ((float)(phase) * DDS_TWO_PI / DDS_PHASE_RESOLUTION)
#define DDS_RAD_TO_PHASE(rad)       ((uint16_t)((rad) * DDS_PHASE_RESOLUTION / DDS_TWO_PI))

/**
  * @}
  */

/* Exported macro ------------------------------------------------------------*/

/** @defgroup DDS_Exported_Macros DDS导出宏
  * @{
  */

/**
  * @brief  计算频率控制字 (兼容模式)
  */
#define DDS_CALC_FREQ_WORD(freq, sample_rate) \
    ((uint32_t)(((uint64_t)(freq) << DDS_PHASE_BITS) / (sample_rate)))

/**
  * @brief  计算32位频率控制字 (快速模式)
  */
#define DDS_CALC_FREQ_WORD_32(freq, sample_rate) \
    ((uint32_t)(((uint64_t)(freq) << DDS_PHASE_BITS_FAST) / (sample_rate)))

/**
  * @brief  计算64位频率控制字 (高精度模式，暂时使用32位兼容)
  */
#define DDS_CALC_FREQ_WORD_64(freq, sample_rate) \
    DDS_CALC_FREQ_WORD(freq, sample_rate)

/**
  * @brief  计算相位增量 (兼容宏)
  */
#define DDS_CALC_PHASE_INC(freq_word) (freq_word)

/**
  * @brief  64位相位累加器到表索引转换
  */
#define DDS_PHASE_TO_INDEX_64(phase_acc, table_bits) \
    ((uint32_t)((phase_acc) >> (DDS_PHASE_BITS - (table_bits))))

/**
  * @brief  32位相位累加器到表索引转换 (快速模式)
  */
#define DDS_PHASE_TO_INDEX_32(phase_acc, table_bits) \
    ((uint32_t)((phase_acc) >> (DDS_PHASE_BITS_FAST - (table_bits))))

/**
  * @brief  相位累加器到表索引转换 (兼容宏)
  */
#define DDS_PHASE_TO_INDEX(phase_acc) \
    DDS_PHASE_TO_INDEX_32((uint32_t)(phase_acc), 12)

/**
  * @brief  获取波形表位数
  */
#define DDS_GET_TABLE_BITS(table_size) \
    ((table_size) == 16384 ? 14 : ((table_size) == 8192 ? 13 : 12))

/**
  * @brief  频率到通道自动选择
  */
#define DDS_AUTO_SELECT_CHANNEL(freq) \
    ((freq) <= DDS_FREQ_THRESHOLD_PRECISION ? DDS_CHANNEL_PRECISION : \
     (freq) <= DDS_FREQ_THRESHOLD_HIGH ? DDS_CHANNEL_HIGH : DDS_CHANNEL_FAST)

/**
  * @brief  检查DDS是否运行
  */
#define DDS_IS_RUNNING(handle)      ((handle)->is_running)

/**
  * @brief  获取当前频率
  */
#define DDS_GET_FREQUENCY(handle)   ((handle)->config.frequency)

/**
  * @brief  获取当前波形类型
  */
#define DDS_GET_WAVE_TYPE(handle)   ((handle)->config.wave_type)

/**
  * @}
  */

/* Exported variables --------------------------------------------------------*/

/** @defgroup DDS_Exported_Variables DDS导出变量
  * @{
  */

extern DDS_Handle_t g_dds_handle;               ///< DDS句柄
extern volatile bool g_dds_update_complete;     ///< 更新完成标志
extern volatile bool g_dds_dma_complete;        ///< DMA传输完成标志

// 预定义波形表
extern const uint16_t g_sin_wave_table[DDS_WAVE_TABLE_SIZE];     ///< 正弦波表
extern const uint16_t g_square_wave_table[DDS_WAVE_TABLE_SIZE];  ///< 方波表
extern const uint16_t g_triangle_wave_table[DDS_WAVE_TABLE_SIZE]; ///< 三角波表
extern const uint16_t g_sawtooth_wave_table[DDS_WAVE_TABLE_SIZE]; ///< 锯齿波表

/**
  * @}
  */

/* Exported functions --------------------------------------------------------*/

/** @defgroup DDS_Exported_Functions DDS导出函数
  * @{
  */

/**
  * @brief  DDS初始化
  * @param  config: 配置参数指针
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_Init(DDS_Config_t* config);

/**
  * @brief  DDS反初始化
  * @param  None
  * @retval None
  */
void DDS_DeInit(void);

/**
  * @brief  开始DDS输出
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_Start(void);

/**
  * @brief  停止DDS输出
  * @param  None
  * @retval None
  */
void DDS_Stop(void);

/**
  * @brief  设置输出频率
  * @param  frequency: 频率(Hz)
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetFrequency(uint32_t frequency);

/**
  * @brief  设置波形类型
  * @param  wave_type: 波形类型
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetWaveType(DDS_WaveType_t wave_type);

/**
  * @brief  设置幅度
  * @param  amplitude: 幅度(0-4095)
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetAmplitude(uint16_t amplitude);

/**
  * @brief  设置直流偏移
  * @param  offset: 偏移(0-4095)
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetOffset(uint16_t offset);

/**
  * @brief  设置相位偏移
  * @param  phase: 相位(0-3599, 对应0-359.9度)
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetPhase(uint16_t phase);

/**
  * @brief  设置输出通道
  * @param  channel: 输出通道
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetChannel(DDS_Channel_t channel);

/**
  * @brief  获取当前通道
  * @param  None
  * @retval 当前输出通道
  */
DDS_Channel_t DDS_GetCurrentChannel(void);

/**
  * @brief  强制通道切换 (带相位同步)
  * @param  target_channel: 目标通道
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SwitchChannel(DDS_Channel_t target_channel);

/**
  * @brief  设置自定义波形
  * @param  wave_table: 波形表指针
  * @param  table_size: 表大小
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetCustomWave(const uint16_t* wave_table, uint32_t table_size);

/**
  * @brief  设置高精度频率 (支持小数)
  * @param  frequency_hz: 频率(Hz，支持小数)
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetFrequencyFloat(float frequency_hz);

/**
  * @brief  获取实际输出频率
  * @param  None
  * @retval 实际频率(Hz)
  */
float DDS_GetActualFrequency(void);

/**
  * @brief  配置调制
  * @param  modulation: 调制参数指针
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_ConfigModulation(DDS_Modulation_t* modulation);

/**
  * @brief  使能/禁用调制
  * @param  enable: true-使能, false-禁用
  * @retval None
  */
void DDS_EnableModulation(bool enable);

/**
  * @brief  获取当前输出值
  * @param  None
  * @retval 当前DAC输出值
  */
uint16_t DDS_GetCurrentOutput(void);

/**
  * @brief  获取统计信息
  * @param  stats: 统计信息结构体指针
  * @retval None
  */
void DDS_GetStats(DDS_Stats_t* stats);

/**
  * @brief  重置统计信息
  * @param  None
  * @retval None
  */
void DDS_ResetStats(void);

/**
  * @brief  计算THD(总谐波失真)
  * @param  None
  * @retval THD百分比
  */
float DDS_CalculateTHD(void);

/**
  * @brief  TIM6中断处理函数 - 内部实现，不对外暴露
  * @param  None
  * @retval None
  * @note   中断处理函数不需要在头文件中声明
  */
// void TIM6_DAC_IRQHandler(void); // 已移除声明，避免符号冲突

/**
  * @brief  DMA1_Stream5中断处理函数
  * @param  None
  * @retval None
  */
void DMA1_Stream5_IRQHandler(void);

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __DDS_WAVEGEN_H */

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
