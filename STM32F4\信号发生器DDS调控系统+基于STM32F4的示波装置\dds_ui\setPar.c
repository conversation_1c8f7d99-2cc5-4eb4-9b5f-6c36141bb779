#include "lcd.h"
#include "key.h"
#include "math.h"
#include "stdio.h"
#include "ad9833.h"
#include "setPar.h"
#include "showPar.h"
#include "setParBmp.h"

extern ddsNode wave;

double keyBoard(unsigned short int inX, unsigned short int inY, double lastNum)
{
	double newNum = 0.0;
	unsigned char flag = 0x00;
	unsigned short int inputTime = 0;
	unsigned short int x = inX, y = inY;
	unsigned short int keyNum = 16;
	unsigned short int number[20];
	unsigned short int pointPosition = 0;
	
	POINT_COLOR = WHITE;
	BACK_COLOR = BLUE;
	LCD_Draw_Picture(760, 445, 800, 485, (u8 *)gImage_keyboard);
	POINT_COLOR = WHITE;
	BACK_COLOR = BLACK;
	
	while(1)
	{
			keyNum = getKeyNum();
			
			switch(keyNum)
			{
				case 0:
				{
					if((number[0] == 0) && (inputTime == 1))			//[0] = 0, i = 1	��Ϊ0
						continue;
					
					number[(flag&0x0f)] = keyNum;
				};break;
				case 1:
					number[(flag&0x0f)] = keyNum;break;
				case 2:
					number[(flag&0x0f)] = keyNum;break;
				case 3:
					number[(flag&0x0f)] = keyNum;break;
				case 4:
					number[(flag&0x0f)] = keyNum;break;
				case 5:
					number[(flag&0x0f)] = keyNum;break;
				case 6:
					number[(flag&0x0f)] = keyNum;break;
				case 7:
					number[(flag&0x0f)] = keyNum;break;
				case 8:
					number[(flag&0x0f)] = keyNum;break;
				case 9:
					number[(flag&0x0f)] = keyNum;break;
				case 10:
				{
					if(inputTime == 0)	
					{
						LCD_Fill(760, 445, 800, 480, BLUE);
						POINT_COLOR = WHITE;
						BACK_COLOR = BLUE;
						
						return lastNum;
					}
					
					BACK_COLOR = BLUE;
					LCD_ShowChar(x -= 10, y, ' ', 24, 0);
					
					inputTime --;
					
					if(inputTime == pointPosition)
					{
						flag &= 0x7f;								//С�������0;
						BACK_COLOR = BLACK;
						continue;
					}
		
					flag--;

					BACK_COLOR = BLACK;
					continue;
				}
				case 11:
				{			
					if(((flag&0x80) == 0x00) && (inputTime != 0))
					{
						LCD_ShowChar(x, y, '.', 24, 0);
						pointPosition = inputTime;
						//printf("С������inputTime=%d\r\n", pointPosition);
						x += 10;
						inputTime++;
						flag |= 0x80;								//С�������1
						continue;
					}
					continue;
				}
				case 14:
				{
					if(inputTime != 0)
					{
						for(int i = 0; i < (flag&0x0f); i++)
						{
							if(i < pointPosition)
								newNum += number[i] * pow(10, pointPosition - i - 1);
							else
								newNum += number[i] / pow(10, i - pointPosition + 1);
						}
						
						LCD_Fill(760, 445, 800, 480, BLUE);
						LCD_Fill(inX, inY, inX+(flag&0x0f)*20, inY+24, BLUE);
						POINT_COLOR = WHITE;
						BACK_COLOR = BLUE;
		
						//printf("newNum = %.2f\r\n", newNum);
						return (double)newNum;						
					}
					continue;
				}
				case 12:
				case 13:
				case 15:
					continue;	
			}
			
			LCD_ShowChar(x, y, number[(flag&0x0f)]+0x30, 24, 0);
			x += 10;
			flag ++;
			inputTime++;
			//printf("inputTime = %d\r\n", inputTime);
			if((flag&0x80) == 0x00)
				pointPosition = (flag&0x0f);
		}			
//				case 10:
//				{
//					if(flag == 0x20)
//					{
//						LCD_Fill(760, 445, 800, 480, BLUE);
//						POINT_COLOR = WHITE;
//						BACK_COLOR = BLUE;
//						
//						return lastNum;
//					}
//					if((flag&0x80) == 0x80)
//					{
//						BACK_COLOR = BLUE;
//						LCD_ShowChar(x -= 10, y, ' ', 24, 0);
//						flag--;
//						
//						if((flag&0x0f) == 0x00)
//							flag |= 0x20;						//��λ��1
//						
//						BACK_COLOR = BLACK;
//						continue;
//					}
//				}
//				case 11:
//				{
//					if(((flag&0x0f) != 0) && ((flag&0x40) == 0x00))		//С�����Ϊ0
//					{
//						LCD_ShowChar(x, y, '.', 24, 0);
//						x += 10;
//						flag |= 0x50;												//С�������1,��������1
//						pointPosition = flag&0x0f;
//						continue;
//					}
//					continue;
//				}
//				case 14:
//				{
//					if((flag&0x80) == 0x80)
//					{
//						for(int i = 0; i < (flag&0x0f); i++)
//						{
//							if(i < pointPosition)
//								newNum += number[i] * pow(10, pointPosition - i -1);
//							else
//								newNum += number[i] / pow(10, i - pointPosition + 1);
//						}
//						
//						LCD_Fill(760, 445, 800, 480, BLUE);
//						LCD_Fill(inX, inY, inX+(flag&0x0f)*20, inY+24, BLUE);
//						POINT_COLOR = WHITE;
//						BACK_COLOR = BLUE;
//		
//						return (double)newNum;
//					}
//					continue;
//				}
//		}
//		
//		LCD_ShowChar(x, y, number[(flag&0x0f)]+0x30, 24, 0);
//		x += 10;
//		flag |= 0x80;				//��������1
//		flag ++;
//		
//		if((flag&0x40) == 0x00)
//			pointPosition = (flag&0x0f);
}

unsigned short int setShape(unsigned short int nowShape)
{
	unsigned short int keyNum = 16;
	unsigned short int setFlag = 1;
	POINT_COLOR = RED;
	LCD_ShowString(630, 140, 125, 24, 24, "Set");
	POINT_COLOR = WHITE;
	
	while(1)
	{	
		if(nowShape == 0)
			LCD_ShowString(420, 140, 125, 24, 24, "TRI_WAVE");
		if(nowShape == 1)
			LCD_ShowString(420, 140, 125, 24, 24, "SIN_WAVE");
		if(nowShape == 2)
			LCD_ShowString(420, 140, 125, 24, 24, "SQU_WAVE");
		
		if(setFlag == 1)
		{
			AD9833_WaveSeting(wave.iFreq, 0, nowShape, wave.iPhase);
			setFlag = 0;
			printf("�������óɹ�\r\n");
		}
		
		keyNum = getKeyNum();
		
		if(keyNum == 12)
			if(nowShape < 2)
			{
				nowShape++;
				setFlag = 1;
			}
		if(keyNum == 13)
			if(nowShape > 0)
			{
				nowShape--;
				setFlag = 1;
			}
		if(keyNum == 10)
			break;
	}
	
	LCD_ShowString(630, 140, 125, 24, 24, "   ");
	return nowShape;
}

double setAmp(double nowAmp)
{
	float amp = 0.0;
	unsigned short int keyNum = 16;
	unsigned short int setFlag = 1;
	unsigned char strAmp[] = "0.00 V";
	
	POINT_COLOR = RED;
	LCD_ShowString(630, 200, 125, 24, 24, "Set");
	POINT_COLOR = WHITE;
	
	printf("����amp���ô��ڵ�ֵ:%d\r\n", wave.iAmp);
	
	while(1)
	{
		amp = (nowAmp * 6.3)/255;
		strAmp[0] = (int)amp + 0x30;
		strAmp[2] = (int)(amp * 10)%10 + 0x30;
		strAmp[3] = (int)(amp * 100)%10 + 0x30;
		LCD_ShowString(420, 200, 100, 24, 24, strAmp);
		
		if(setFlag == 1)
		{
			AD9833_AmpSet(nowAmp);
			setFlag = 0;
		}
		
		keyNum = getKeyNum();						//��ȡ����ֵ

		if(keyNum == 10)
			break;
		if(keyNum == 12)
			if(nowAmp < 255)
			{
				nowAmp++;
				setFlag = 1;
				printf("nowAmp:%.2f \r\n", nowAmp);
			}
		if(keyNum == 13)
			if(nowAmp > 0)
			{
				nowAmp--;
				setFlag = 1;
				printf("nowAmp:%.2f \r\n", nowAmp);
			}
		if(keyNum == 14)
		{
			nowAmp = (keyBoard(400, 230, (nowAmp*6.5)/255)/6.5) * 255;
			setFlag = 1;
			printf("nowAmp:%.2f	\r\n", nowAmp);
		}
	}
	
	LCD_ShowString(630, 200, 125, 24, 24, "   ");
	return (double)nowAmp;
}

double setFreq(double nowFreq)
{
	printf("����Ƶ�����ô���\r\n");
	
	unsigned short int keyNum = 16;
	unsigned short int setFlag = 0;
	float freq = 0.0;
	unsigned char strFreq[] = "000.00";
	
	POINT_COLOR = RED;
	LCD_ShowString(630, 260, 125, 24, 24, "Set");
	POINT_COLOR = WHITE;
	
	while(1)
	{
			if(nowFreq < 1000)
			{
				freq = (float)nowFreq;
				strFreq[0] = (int)freq/100 + 0x30;
				strFreq[1] = ((int)freq%100)/10 + 0x30;
				strFreq[2] = ((int)freq%10) + 0x30;
				strFreq[4] = (int)(freq*10) % 10 + 0x30;
				strFreq[5] = (int)(freq*100) % 10 + 0x30;
				LCD_ShowString(420, 260, 100, 24, 24, strFreq);
				LCD_ShowString(500, 260, 100, 24, 24, "Hz   ");
			}
			if(nowFreq < 1000000 && nowFreq >= 1000)
			{
				freq = (float)nowFreq / 1000;
				strFreq[0] = (int)freq/100 + 0x30;
				strFreq[1] = ((int)freq%100)/10 + 0x30;
				strFreq[2] = ((int)freq%10) + 0x30;
				strFreq[4] = (int)(freq*10) % 10 + 0x30;
				strFreq[5] = (int)(freq*100) % 10 + 0x30;
				LCD_ShowString(420, 260, 100, 24, 24, strFreq);
				LCD_ShowString(500, 260, 100, 24, 24, "KHz");
			}
			if(nowFreq >= 1000000)
			{
				freq = (float)nowFreq / 1000000;
				strFreq[0] = (int)freq/100 + 0x30;
				strFreq[1] = ((int)freq%100)/10 + 0x30;
				strFreq[2] = ((int)freq%10) + 0x30;
				strFreq[4] = (int)(freq*10) % 10 + 0x30;
				strFreq[5] = (int)(freq*100) % 10 + 0x30;
				LCD_ShowString(420, 260, 100, 24, 24, strFreq);
				LCD_ShowString(500, 260, 100, 24, 24, "MHz");
			}
	
			if(strFreq[0] == 0x30)
				LCD_ShowString(420, 260, 20, 24, 24, " ");
			if(strFreq[0] == 0x30 && strFreq[1] == 0x30)
				LCD_ShowString(420, 260, 20, 24, 24, "  ");
			
			if(setFlag == 1)
			{
				AD9833_WaveSeting(nowFreq, 0, wave.iShape, wave.iPhase);
				setFlag = 0;
				printf("Ƶ���������\r\n");
			}
		
			keyNum = getKeyNum();
			
			if(keyNum == 10)
				break;
			if(keyNum == 12)
				if(nowFreq < 12500000)
				{
					nowFreq += 0.1;
					setFlag = 1;
					printf("freq:%.2f\r\n", nowFreq);
				}
			if(keyNum == 13)
				if(nowFreq > 0)
				{
					nowFreq -= 0.1;
					setFlag = 1;
					printf("freq:%.2f\r\n", nowFreq);
				}
			if(keyNum == 14)
			{
				nowFreq = (double)keyBoard(400, 290, nowFreq);
				setFlag = 1;
				printf("freq:%.2f\r\n", nowFreq);
			}
	}
	
	LCD_ShowString(630, 260, 125, 24, 24, "   ");
	return nowFreq;
}

double setPhase(double nowPhase)
{
	float pha = 0.0;
	unsigned short int keyNum = 16;
	unsigned short int setFlag = 0;
	unsigned char strPha[] = "000.0 deg";
	
	POINT_COLOR = RED;
	LCD_ShowString(630, 320, 125, 24, 24, "Set");
	POINT_COLOR = WHITE;
	
	while(1)
	{
		pha = (float)nowPhase;
		strPha[0] = (int)pha/100 + 0x30;
		strPha[1] = ((int)pha%100) / 10 + 0x30;
		strPha[2] = ((int)pha%10) + 0x30;
		strPha[4] = (int)(pha*10) % 10 + 0x30;
		LCD_ShowString(420, 320, 100, 24, 24, strPha);
		
		if(strPha[0] == 0x30)
			LCD_ShowString(420, 320, 20, 24, 24, " ");
		if(strPha[0] == 0x30 && strPha[1] == 0x30)
			LCD_ShowString(420, 320, 20, 24, 24, "  ");
		
		if(setFlag == 1)
			{
				AD9833_WaveSeting(wave.iFreq, 0, wave.iShape, nowPhase);
				setFlag = 0;
				printf("��λ�������\r\n");
			}
		
			keyNum = getKeyNum();
			
			if(keyNum == 10)
				break;
			if(keyNum == 12)
				if(nowPhase < 90)
				{
					nowPhase += 0.1;
					setFlag = 1;
					printf("phase:%.2f\r\n", nowPhase);
				}
			if(keyNum == 13)
				if(nowPhase > 0)
				{
					nowPhase -= 0.1;
					setFlag = 1;
					printf("phase:%.2f\r\n", nowPhase);
				}
			if(keyNum == 14)
			{
				nowPhase = (double)keyBoard(400, 350, nowPhase);
			}
	}
	
	LCD_ShowString(630, 320, 125, 24, 24, "   ");
	return (double)nowPhase;
}

void ddsSetWindow(void)
{
	unsigned int rowNum = 140;
	unsigned int parFlag = 0;
	unsigned short keyNum;
	
	POINT_COLOR = RED;
	LCD_Draw_Picture(141, 59, 141+500, 59+60, (unsigned char *)gImage_ddsSetTitle);
	
	POINT_COLOR = WHITE;
	LCD_ShowString(600, rowNum, 20, 20, 24, "<-");
	
	while(1)
	{
		keyNum = getKeyNum();
		
		if(keyNum == 13)		//����
		{
			if(rowNum >= 320)
				LCD_ShowString(600, rowNum, 20, 20, 24, "<-");
			if(rowNum < 320)
			{
				LCD_ShowString(600, rowNum, 20, 20, 24, "  ");
				LCD_ShowString(600, rowNum+=60, 20, 20, 24, "<-");
				parFlag++;
				printf("����	rowNum:%d	\r\n", rowNum);
			}
		}
		
		if(keyNum == 12)			//����
		{
			if(rowNum <= 140)
				LCD_ShowString(600, rowNum, 20, 20, 24, "<-");
			if(rowNum > 140)
			{
				LCD_ShowString(600, rowNum, 20, 20, 24, "  ");
				LCD_ShowString(600, rowNum-=60, 20, 20, 24, "<-");
				parFlag--; 
				printf("����	rowNum:%d	\r\n", rowNum);
			}
		}
		
		if(keyNum == 14)			//ȷ�ϼ�
		{
			switch(parFlag)
			{
				case 0:
					wave.iShape = setShape(wave.iShape);break;
				case 1:
					wave.iAmp = setAmp(wave.iAmp);break;
				case 2:
					wave.iFreq = setFreq(wave.iFreq);break;
				case 3:
					wave.iPhase = setPhase(wave.iPhase);break;
				default:
					continue;
			}
		}
		
		if(keyNum == 10)			//�˳���
			break;
	}
	
	LCD_ShowString(600, rowNum, 20, 20, 24, "  ");
}



