/**
  ******************************************************************************
  * @file    dds_wavegen.c
  * <AUTHOR>
  * @version V1.0
  * @date    2024
  * @brief   DDS波形生成模块实现
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "dds_wavegen.h"
#include "systick.h"
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/

/** @defgroup DDS_Private_Defines DDS私有定义
  * @{
  */

#define DAC_GPIO_PORT               GPIOA
#define DAC_GPIO_CLK                RCC_AHB1Periph_GPIOA
#define DAC_PIN                     GPIO_Pin_4   ///< PA4 - DAC1_OUT

#define DAC_CLK                     RCC_APB1Periph_DAC
#define TIM_CLK                     RCC_APB1Periph_TIM6
#define DMA_CLK                     RCC_AHB1Periph_DMA1

#define DDS_TIM                     TIM6
#define DDS_DAC_CHANNEL             DAC_Channel_1
#define DDS_DMA_STREAM              DMA1_Stream5
#define DDS_DMA_CHANNEL             DMA_Channel_7

// 波形生成缓冲区大小
#define DDS_OUTPUT_BUFFER_SIZE      512U

/**
  * @}
  */

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/

/** @defgroup DDS_Private_Variables DDS私有变量
  * @{
  */

DDS_Handle_t g_dds_handle;                      ///< DDS句柄
volatile bool g_dds_update_complete = false;    ///< 更新完成标志
volatile bool g_dds_dma_complete = false;       ///< DMA传输完成标志

// 三重缓冲输出缓冲区 (极速段专用)
#define DDS_BUFFER_SIZE_FAST        1024U
#define DDS_BUFFER_COUNT            3
static uint16_t s_dds_output_buffer_fast[DDS_BUFFER_COUNT][DDS_BUFFER_SIZE_FAST] __attribute__((aligned(32)));
static uint8_t s_current_buffer_index = 0;

// 传统输出缓冲区 (兼容现有代码)
#define DDS_OUTPUT_BUFFER_SIZE      512U
static uint16_t s_dds_output_buffer[DDS_OUTPUT_BUFFER_SIZE];

// 三段式预定义波形表 - 内存对齐优化
// 精密段正弦波表 (16384点，超高精度)
const uint16_t g_sin_wave_table_precision[DDS_WAVE_TABLE_SIZE_PRECISION] __attribute__((aligned(32))) = {
    // 16384点正弦波表，中心值2048，幅度2047
    // 生成公式：sin_table[i] = (uint16_t)(2048 + 2047 * sin(2*PI*i/16384))
    2048, 2048, 2049, 2049, 2050, 2050, 2051, 2051, 2052, 2052, 2053, 2053, 2054, 2054, 2055, 2055,
    2056, 2056, 2057, 2057, 2058, 2058, 2059, 2059, 2060, 2060, 2061, 2061, 2062, 2062, 2063, 2063,
    // 注意：实际应用中需要完整的16384点表
    // 这里为了代码空间使用简化版本，实际部署时需要完整表
};

// 高精度段正弦波表 (8192点，保持现有精度)
const uint16_t g_sin_wave_table_high[DDS_WAVE_TABLE_SIZE_HIGH] __attribute__((aligned(32))) = {
    // 8192点正弦波表，中心值2048，幅度2047
    // 生成公式：sin_table[i] = (uint16_t)(2048 + 2047 * sin(2*PI*i/8192))
    2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063,
    2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079,
    // 注意：实际应用中需要完整的8192点表
};

// 极速段正弦波表 (4096点，速度优化)
const uint16_t g_sin_wave_table_fast[DDS_WAVE_TABLE_SIZE_FAST] __attribute__((aligned(32))) = {
    // 4096点正弦波表，中心值2048，幅度2047
    // 生成公式：sin_table[i] = (uint16_t)(2048 + 2047 * sin(2*PI*i/4096))
    2048, 2051, 2054, 2057, 2060, 2063, 2066, 2069, 2072, 2075, 2078, 2081, 2084, 2087, 2090, 2093,
    2096, 2099, 2102, 2105, 2108, 2111, 2114, 2117, 2120, 2123, 2126, 2129, 2132, 2135, 2138, 2141,
    2144, 2147, 2150, 2153, 2156, 2159, 2162, 2165, 2168, 2171, 2174, 2177, 2180, 2183, 2186, 2189,
    2192, 2195, 2198, 2201, 2204, 2207, 2210, 2213, 2216, 2219, 2222, 2225, 2228, 2231, 2234, 2237,
    // 注意：实际应用中需要完整的4096点表
};

// 兼容性定义 (保持现有代码兼容)
const uint16_t g_sin_wave_table[DDS_WAVE_TABLE_SIZE] = {
    // 指向高精度段表的前4096个点
    2048, 2051, 2054, 2057, 2060, 2063, 2066, 2069, 2072, 2075, 2078, 2081, 2084, 2087, 2090, 2093,
    // 实际实现中这里应该是完整的表或者指针重定向
};

// 简化的波形表定义（实际应用中需要完整实现）
// 方波表 - 简化实现
const uint16_t g_square_wave_table[DDS_WAVE_TABLE_SIZE] = {
    // 前半周期高电平，后半周期低电平
    4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
    4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
    4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
    4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
    4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
    4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
    4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
    4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095, 4095,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
};

// 三角波表 - 简化实现
const uint16_t g_triangle_wave_table[DDS_WAVE_TABLE_SIZE] = {
    // 线性上升和下降
    0, 32, 64, 96, 128, 160, 192, 224, 256, 288, 320, 352, 384, 416, 448, 480,
    512, 544, 576, 608, 640, 672, 704, 736, 768, 800, 832, 864, 896, 928, 960, 992,
    1024, 1056, 1088, 1120, 1152, 1184, 1216, 1248, 1280, 1312, 1344, 1376, 1408, 1440, 1472, 1504,
    1536, 1568, 1600, 1632, 1664, 1696, 1728, 1760, 1792, 1824, 1856, 1888, 1920, 1952, 1984, 2016,
    2048, 2080, 2112, 2144, 2176, 2208, 2240, 2272, 2304, 2336, 2368, 2400, 2432, 2464, 2496, 2528,
    2560, 2592, 2624, 2656, 2688, 2720, 2752, 2784, 2816, 2848, 2880, 2912, 2944, 2976, 3008, 3040,
    3072, 3104, 3136, 3168, 3200, 3232, 3264, 3296, 3328, 3360, 3392, 3424, 3456, 3488, 3520, 3552,
    3584, 3616, 3648, 3680, 3712, 3744, 3776, 3808, 3840, 3872, 3904, 3936, 3968, 4000, 4032, 4064,
    4095, 4064, 4032, 4000, 3968, 3936, 3904, 3872, 3840, 3808, 3776, 3744, 3712, 3680, 3648, 3616,
    3584, 3552, 3520, 3488, 3456, 3424, 3392, 3360, 3328, 3296, 3264, 3232, 3200, 3168, 3136, 3104,
    3072, 3040, 3008, 2976, 2944, 2912, 2880, 2848, 2816, 2784, 2752, 2720, 2688, 2656, 2624, 2592,
    2560, 2528, 2496, 2464, 2432, 2400, 2368, 2336, 2304, 2272, 2240, 2208, 2176, 2144, 2112, 2080,
    2048, 2016, 1984, 1952, 1920, 1888, 1856, 1824, 1792, 1760, 1728, 1696, 1664, 1632, 1600, 1568,
    1536, 1504, 1472, 1440, 1408, 1376, 1344, 1312, 1280, 1248, 1216, 1184, 1152, 1120, 1088, 1056,
    1024, 992, 960, 928, 896, 864, 832, 800, 768, 736, 704, 672, 640, 608, 576, 544,
    512, 480, 448, 416, 384, 352, 320, 288, 256, 224, 192, 160, 128, 96, 64, 32
};

// 锯齿波表 - 简化实现
const uint16_t g_sawtooth_wave_table[DDS_WAVE_TABLE_SIZE] = {
    // 线性递增
    0, 16, 32, 48, 64, 80, 96, 112, 128, 144, 160, 176, 192, 208, 224, 240,
    256, 272, 288, 304, 320, 336, 352, 368, 384, 400, 416, 432, 448, 464, 480, 496,
    512, 528, 544, 560, 576, 592, 608, 624, 640, 656, 672, 688, 704, 720, 736, 752,
    768, 784, 800, 816, 832, 848, 864, 880, 896, 912, 928, 944, 960, 976, 992, 1008,
    1024, 1040, 1056, 1072, 1088, 1104, 1120, 1136, 1152, 1168, 1184, 1200, 1216, 1232, 1248, 1264,
    1280, 1296, 1312, 1328, 1344, 1360, 1376, 1392, 1408, 1424, 1440, 1456, 1472, 1488, 1504, 1520,
    1536, 1552, 1568, 1584, 1600, 1616, 1632, 1648, 1664, 1680, 1696, 1712, 1728, 1744, 1760, 1776,
    1792, 1808, 1824, 1840, 1856, 1872, 1888, 1904, 1920, 1936, 1952, 1968, 1984, 2000, 2016, 2032,
    2048, 2064, 2080, 2096, 2112, 2128, 2144, 2160, 2176, 2192, 2208, 2224, 2240, 2256, 2272, 2288,
    2304, 2320, 2336, 2352, 2368, 2384, 2400, 2416, 2432, 2448, 2464, 2480, 2496, 2512, 2528, 2544,
    2560, 2576, 2592, 2608, 2624, 2640, 2656, 2672, 2688, 2704, 2720, 2736, 2752, 2768, 2784, 2800,
    2816, 2832, 2848, 2864, 2880, 2896, 2912, 2928, 2944, 2960, 2976, 2992, 3008, 3024, 3040, 3056,
    3072, 3088, 3104, 3120, 3136, 3152, 3168, 3184, 3200, 3216, 3232, 3248, 3264, 3280, 3296, 3312,
    3328, 3344, 3360, 3376, 3392, 3408, 3424, 3440, 3456, 3472, 3488, 3504, 3520, 3536, 3552, 3568,
    3584, 3600, 3616, 3632, 3648, 3664, 3680, 3696, 3712, 3728, 3744, 3760, 3776, 3792, 3808, 3824,
    3840, 3856, 3872, 3888, 3904, 3920, 3936, 3952, 3968, 3984, 4000, 4016, 4032, 4048, 4064, 4080
};

/**
  * @}
  */

/* Private function prototypes -----------------------------------------------*/
// 传统DAC配置 (保持兼容)
static void DDS_GPIO_Config(void);
static void DDS_DAC_Config(void);
static void DDS_TIM_Config(uint32_t sample_rate);
static void DDS_DMA_Config(void);
static void DDS_NVIC_Config(void);

// 三段式架构新增函数
static void DDS_Internal_DAC_Config(void);
static void DDS_Internal_DMA_Config(void);
static void DDS_TIM_Fast_Config(uint32_t sample_rate);
static void DDS_Channel_Switch_Config(void);

// DDS核心算法
static void DDS_GenerateWaveTable(DDS_WaveType_t wave_type);
static uint16_t DDS_GetNextSample(void);
static uint16_t DDS_GetNextSample_Fast(void);
static uint16_t DDS_LinearInterpolation(uint32_t phase_acc);
static uint16_t DDS_LinearInterpolation_64(uint64_t phase_acc, uint32_t table_size);
static void DDS_UpdateFrequencyWord(void);
static void DDS_UpdateFrequencyWord_64(void);
static void DDS_ProcessModulation(void);

// 通道管理
static int8_t DDS_SelectOptimalChannel(uint32_t frequency);
static void DDS_SwitchChannel_Internal(DDS_Channel_t target_channel);
static void DDS_SyncPhase_CrossChannel(void);

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  DDS GPIO配置
  * @param  None
  * @retval None
  */
static void DDS_GPIO_Config(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIO时钟
    RCC_AHB1PeriphClockCmd(DAC_GPIO_CLK, ENABLE);
    
    // 配置DAC输出引脚为模拟模式
    GPIO_InitStructure.GPIO_Pin = DAC_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(DAC_GPIO_PORT, &GPIO_InitStructure);
}

/**
  * @brief  DDS DAC配置
  * @param  None
  * @retval None
  */
static void DDS_DAC_Config(void)
{
    DAC_InitTypeDef DAC_InitStructure;
    
    // 使能DAC时钟
    RCC_APB1PeriphClockCmd(DAC_CLK, ENABLE);
    
    // 配置DAC
    DAC_InitStructure.DAC_Trigger = DAC_Trigger_T6_TRGO;  // TIM6触发
    DAC_InitStructure.DAC_WaveGeneration = DAC_WaveGeneration_None;
    DAC_InitStructure.DAC_LFSRUnmask_TriangleAmplitude = DAC_LFSRUnmask_Bit0;
    DAC_InitStructure.DAC_OutputBuffer = DAC_OutputBuffer_Enable;
    DAC_Init(DDS_DAC_CHANNEL, &DAC_InitStructure);
    
    // 使能DAC DMA
    DAC_DMACmd(DDS_DAC_CHANNEL, ENABLE);
    
    // 使能DAC通道
    DAC_Cmd(DDS_DAC_CHANNEL, ENABLE);
}

/**
  * @brief  DDS定时器配置 (传统模式，保持兼容)
  * @param  sample_rate: 采样率
  * @retval None
  */
static void DDS_TIM_Config(uint32_t sample_rate)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;

    // 使能TIM6时钟
    RCC_APB1PeriphClockCmd(TIM_CLK, ENABLE);

    // 计算定时器参数
    uint32_t timer_freq = 84000000; // APB1时钟84MHz
    uint32_t period = timer_freq / sample_rate;

    // 配置定时器基本参数
    TIM_TimeBaseStructure.TIM_Period = period - 1;
    TIM_TimeBaseStructure.TIM_Prescaler = 0;
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(DDS_TIM, &TIM_TimeBaseStructure);

    // 配置TIM6触发输出
    TIM_SelectOutputTrigger(DDS_TIM, TIM_TRGOSource_Update);

    // 使能定时器
    TIM_Cmd(DDS_TIM, ENABLE);
}

/**
  * @brief  内置DAC1配置 (极速段专用)
  * @param  None
  * @retval None
  */
static void DDS_Internal_DAC_Config(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    DAC_InitTypeDef DAC_InitStructure;

    // 使能时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_DAC, ENABLE);

    // 配置PA4为DAC1_OUT (模拟模式)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_4;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // 配置DAC1 - 极限性能模式
    DAC_InitStructure.DAC_Trigger = DAC_Trigger_T2_TRGO;  // TIM2触发 (高速)
    DAC_InitStructure.DAC_WaveGeneration = DAC_WaveGeneration_None;
    DAC_InitStructure.DAC_LFSRUnmask_TriangleAmplitude = DAC_LFSRUnmask_Bit0;
    DAC_InitStructure.DAC_OutputBuffer = DAC_OutputBuffer_Enable;  // 使能输出缓冲
    DAC_Init(DAC_Channel_1, &DAC_InitStructure);

    // 使能DAC DMA
    DAC_DMACmd(DAC_Channel_1, ENABLE);

    // 使能DAC通道1
    DAC_Cmd(DAC_Channel_1, ENABLE);
}

/**
  * @brief  极速段定时器配置 (20MHz采样率)
  * @param  sample_rate: 采样率
  * @retval None
  */
static void DDS_TIM_Fast_Config(uint32_t sample_rate)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;

    // 使能TIM2时钟 (APB1, 84MHz)
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);

    // 计算定时器参数 - 极限性能
    uint32_t timer_freq = 84000000; // APB1时钟84MHz
    uint32_t period = timer_freq / sample_rate;

    // 确保period不小于4 (硬件限制)
    if (period < 4) period = 4;  // 最高21MHz采样率

    // 配置定时器基本参数
    TIM_TimeBaseStructure.TIM_Period = period - 1;
    TIM_TimeBaseStructure.TIM_Prescaler = 0;  // 无预分频，最高速度
    TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(TIM2, &TIM_TimeBaseStructure);

    // 配置TIM2触发输出
    TIM_SelectOutputTrigger(TIM2, TIM_TRGOSource_Update);

    // 使能定时器
    TIM_Cmd(TIM2, ENABLE);
}

/**
  * @brief  DDS DMA配置 (传统模式，保持兼容)
  * @param  None
  * @retval None
  */
static void DDS_DMA_Config(void)
{
    DMA_InitTypeDef DMA_InitStructure;

    // 使能DMA时钟
    RCC_AHB1PeriphClockCmd(DMA_CLK, ENABLE);

    // 配置DMA
    DMA_DeInit(DDS_DMA_STREAM);
    DMA_InitStructure.DMA_Channel = DDS_DMA_CHANNEL;
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&DAC->DHR12R1;
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)s_dds_output_buffer;
    DMA_InitStructure.DMA_DIR = DMA_DIR_MemoryToPeripheral;
    DMA_InitStructure.DMA_BufferSize = DDS_OUTPUT_BUFFER_SIZE;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;  // 循环模式
    DMA_InitStructure.DMA_Priority = DMA_Priority_High;
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_HalfFull;
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;
    DMA_Init(DDS_DMA_STREAM, &DMA_InitStructure);

    // 使能DMA中断
    DMA_ITConfig(DDS_DMA_STREAM, DMA_IT_TC, ENABLE);   // 传输完成中断
    DMA_ITConfig(DDS_DMA_STREAM, DMA_IT_HT, ENABLE);   // 半传输中断
}

/**
  * @brief  内置DAC DMA配置 (极速段专用，三重缓冲)
  * @param  None
  * @retval None
  */
static void DDS_Internal_DMA_Config(void)
{
    DMA_InitTypeDef DMA_InitStructure;

    // 使能DMA1时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA1, ENABLE);

    // 配置DMA1_Stream6 (DAC1专用通道)
    DMA_DeInit(DMA1_Stream6);
    DMA_InitStructure.DMA_Channel = DMA_Channel_7;  // DAC1对应通道7
    DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&DAC->DHR12R1;
    DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)s_dds_output_buffer_fast[0];
    DMA_InitStructure.DMA_DIR = DMA_DIR_MemoryToPeripheral;
    DMA_InitStructure.DMA_BufferSize = DDS_BUFFER_SIZE_FAST;
    DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;
    DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;
    DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;
    DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;
    DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;  // 循环模式
    DMA_InitStructure.DMA_Priority = DMA_Priority_VeryHigh;  // 最高优先级

    // 极限性能FIFO配置
    DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Enable;
    DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_Full;
    DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_INC4;     // 4字突发传输
    DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;

    DMA_Init(DMA1_Stream6, &DMA_InitStructure);

    // 使能双缓冲模式 (零延迟切换)
    DMA_DoubleBufferModeConfig(DMA1_Stream6, (uint32_t)s_dds_output_buffer_fast[1], DMA_Memory_0);
    DMA_DoubleBufferModeCmd(DMA1_Stream6, ENABLE);

    // 使能DMA中断
    DMA_ITConfig(DMA1_Stream6, DMA_IT_TC, ENABLE);   // 传输完成中断
    DMA_ITConfig(DMA1_Stream6, DMA_IT_HT, ENABLE);   // 半传输中断

    // 保存DMA流到句柄
    g_dds_handle.dma_fast = DMA1_Stream6;
}

/**
  * @brief  DDS NVIC配置
  * @param  None
  * @retval None
  */
static void DDS_NVIC_Config(void)
{
    NVIC_InitTypeDef NVIC_InitStructure;
    
    // 配置TIM6中断
    NVIC_InitStructure.NVIC_IRQChannel = TIM6_DAC_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
    
    // 配置DMA中断
    NVIC_InitStructure.NVIC_IRQChannel = DMA1_Stream5_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
}

/**
  * @brief  生成波形表
  * @param  wave_type: 波形类型
  * @retval None
  */
static void DDS_GenerateWaveTable(DDS_WaveType_t wave_type)
{
    // 这里可以动态生成波形表，或者选择预定义的表
    switch (wave_type) {
        case DDS_WAVE_SINE:
            g_dds_handle.current_wave_table = (uint16_t*)g_sin_wave_table;
            break;
        case DDS_WAVE_SQUARE:
            g_dds_handle.current_wave_table = (uint16_t*)g_square_wave_table;
            break;
        case DDS_WAVE_TRIANGLE:
            g_dds_handle.current_wave_table = (uint16_t*)g_triangle_wave_table;
            break;
        case DDS_WAVE_SAWTOOTH:
            g_dds_handle.current_wave_table = (uint16_t*)g_sawtooth_wave_table;
            break;
        default:
            g_dds_handle.current_wave_table = (uint16_t*)g_sin_wave_table;
            break;
    }
    
    g_dds_handle.wave_table_size = DDS_WAVE_TABLE_SIZE;
}

/**
  * @brief  获取下一个采样点
  * @param  None
  * @retval 采样值
  */
static uint16_t DDS_GetNextSample(void)
{
    uint16_t sample;
    
    // 处理调制
    if (g_dds_handle.config.enable_modulation) {
        DDS_ProcessModulation();
    }
    
    // 获取波形采样点
    if (g_dds_handle.config.enable_interpolation) {
        sample = DDS_LinearInterpolation(g_dds_handle.phase_accumulator);
    } else {
        uint32_t index = DDS_PHASE_TO_INDEX(g_dds_handle.phase_accumulator);
        sample = g_dds_handle.current_wave_table[index];
    }
    
    // 应用幅度和偏移
    sample = (uint16_t)((sample * g_dds_handle.config.amplitude) / DDS_AMPLITUDE_MAX);
    sample += g_dds_handle.config.offset;
    
    // 限制范围
    if (sample > DDS_AMPLITUDE_MAX) {
        sample = DDS_AMPLITUDE_MAX;
    }
    
    // 更新相位累加器
    g_dds_handle.phase_accumulator += g_dds_handle.frequency_word;
    
    // 更新统计信息
    g_dds_handle.stats.output_samples++;
    
    return sample;
}

/**
  * @brief  线性插值
  * @param  phase_acc: 相位累加器值
  * @retval 插值后的采样值
  */
static uint16_t DDS_LinearInterpolation(uint32_t phase_acc)
{
    uint32_t index = DDS_PHASE_TO_INDEX(phase_acc);
    uint32_t next_index = (index + 1) % g_dds_handle.wave_table_size;
    
    // 计算插值权重
    uint32_t frac = (phase_acc >> (DDS_PHASE_BITS - 20)) & 0xFF; // 8位小数部分
    
    uint16_t sample1 = g_dds_handle.current_wave_table[index];
    uint16_t sample2 = g_dds_handle.current_wave_table[next_index];
    
    // 线性插值
    uint32_t result = sample1 + ((sample2 - sample1) * frac) / 256;
    
    return (uint16_t)result;
}

/**
  * @brief  更新频率控制字 (传统32位模式，保持兼容)
  * @param  None
  * @retval None
  */
static void DDS_UpdateFrequencyWord(void)
{
    g_dds_handle.frequency_word = DDS_CALC_FREQ_WORD_32(
        g_dds_handle.config.frequency,
        g_dds_handle.config.sample_rate
    );
}

/**
  * @brief  更新64位频率控制字 (高精度模式)
  * @param  None
  * @retval None
  */
static void DDS_UpdateFrequencyWord_64(void)
{
    // 根据当前通道选择合适的采样率
    uint32_t sample_rate;
    switch (g_dds_handle.current_channel) {
        case DDS_CHANNEL_PRECISION:
            sample_rate = DDS_SAMPLE_RATE_PRECISION;
            break;
        case DDS_CHANNEL_HIGH:
            sample_rate = DDS_SAMPLE_RATE_HIGH;
            break;
        case DDS_CHANNEL_FAST:
            sample_rate = DDS_SAMPLE_RATE_FAST;
            // 同时更新32位快速模式
            g_dds_handle.freq_word_fast = DDS_CALC_FREQ_WORD_32(
                g_dds_handle.config.frequency, sample_rate);
            break;
        default:
            sample_rate = g_dds_handle.config.sample_rate;
            break;
    }

    // 计算64位高精度频率控制字
    g_dds_handle.frequency_word = DDS_CALC_FREQ_WORD_64(
        g_dds_handle.config.frequency, sample_rate
    );
}

/**
  * @brief  获取下一个采样点 (极速段专用)
  * @param  None
  * @retval 采样值
  */
static uint16_t DDS_GetNextSample_Fast(void)
{
    uint16_t sample;

    // 使用32位相位累加器 (速度优化)
    uint32_t index = DDS_PHASE_TO_INDEX_32(g_dds_handle.phase_acc_fast, 12);
    sample = g_dds_handle.wave_table_fast[index];

    // 应用幅度缩放 (快速算法)
    sample = (uint16_t)((sample * g_dds_handle.config.amplitude) >> 12);
    sample += g_dds_handle.config.offset;

    // 限制范围
    if (sample > 4095) sample = 4095;

    // 更新32位相位累加器
    g_dds_handle.phase_acc_fast += g_dds_handle.freq_word_fast;

    return sample;
}

/**
  * @brief  64位线性插值 (高精度模式)
  * @param  phase_acc: 64位相位累加器值
  * @param  table_size: 波形表大小
  * @retval 插值后的采样值
  */
static uint16_t DDS_LinearInterpolation_64(uint64_t phase_acc, uint32_t table_size)
{
    uint32_t table_bits = DDS_GET_TABLE_BITS(table_size);
    uint32_t index = DDS_PHASE_TO_INDEX_64(phase_acc, table_bits);
    uint32_t next_index = (index + 1) % table_size;

    // 计算高精度插值权重 (使用64位精度)
    uint32_t frac_bits = 16;  // 16位小数精度
    uint64_t frac_mask = (1ULL << frac_bits) - 1;
    uint32_t frac = (uint32_t)((phase_acc >> (DDS_PHASE_BITS - table_bits - frac_bits)) & frac_mask);

    uint16_t sample1 = g_dds_handle.current_wave_table[index];
    uint16_t sample2 = g_dds_handle.current_wave_table[next_index];

    // 高精度线性插值
    uint32_t result = sample1 + (((uint32_t)(sample2 - sample1) * frac) >> frac_bits);

    return (uint16_t)result;
}

/**
  * @brief  处理调制 (保持兼容)
  * @param  None
  * @retval None
  */
static void DDS_ProcessModulation(void)
{
    // 简化的调制处理
    if (g_dds_handle.modulation.fm_enable) {
        // 频率调制
        uint32_t mod_index = DDS_PHASE_TO_INDEX(g_dds_handle.mod_phase_acc);
        int16_t mod_value = (int16_t)g_sin_wave_table[mod_index] - 2048;

        // 应用调制深度
        int32_t freq_deviation = (int32_t)g_dds_handle.config.frequency *
                                 g_dds_handle.modulation.mod_depth * mod_value / (100 * 2048);

        uint32_t modulated_freq = g_dds_handle.config.frequency + freq_deviation;
        g_dds_handle.frequency_word = DDS_CALC_FREQ_WORD_32(modulated_freq, g_dds_handle.config.sample_rate);

        // 更新调制相位累加器
        g_dds_handle.mod_phase_acc += g_dds_handle.mod_freq_word;
    }
}

/**
  * @brief  自动选择最优通道
  * @param  frequency: 目标频率
  * @retval 0: 成功, -1: 失败
  */
static int8_t DDS_SelectOptimalChannel(uint32_t frequency)
{
    DDS_Channel_t optimal_channel = DDS_AUTO_SELECT_CHANNEL(frequency);

    // 如果需要切换通道
    if (optimal_channel != g_dds_handle.current_channel) {
        if (g_dds_handle.config.enable_auto_switch) {
            DDS_SwitchChannel_Internal(optimal_channel);
            return 0;
        } else {
            // 不允许自动切换，检查当前通道是否能支持
            switch (g_dds_handle.current_channel) {
                case DDS_CHANNEL_PRECISION:
                    return (frequency <= DDS_FREQ_THRESHOLD_PRECISION) ? 0 : -1;
                case DDS_CHANNEL_HIGH:
                    return (frequency <= DDS_FREQ_THRESHOLD_HIGH) ? 0 : -1;
                case DDS_CHANNEL_FAST:
                    return (frequency <= DDS_MAX_FREQUENCY) ? 0 : -1;
                default:
                    return -1;
            }
        }
    }

    return 0;
}

/**
  * @brief  内部通道切换实现
  * @param  target_channel: 目标通道
  * @retval None
  */
static void DDS_SwitchChannel_Internal(DDS_Channel_t target_channel)
{
    if (target_channel == g_dds_handle.current_channel) {
        return;  // 无需切换
    }

    // 设置切换标志
    g_dds_handle.channel_switching = true;
    g_dds_handle.target_channel = target_channel;

    // 保存当前相位 (用于相位连续性)
    if (g_dds_handle.config.enable_phase_continuity) {
        g_dds_handle.channel_switch_phase = (uint32_t)(g_dds_handle.phase_accumulator >> 32);
    }

    // 停止当前通道
    switch (g_dds_handle.current_channel) {
        case DDS_CHANNEL_PRECISION:
        case DDS_CHANNEL_HIGH:
            // 停止DAC8552输出 (保持现有实现)
            break;
        case DDS_CHANNEL_FAST:
            // 停止内置DAC
            DMA_Cmd(g_dds_handle.dma_fast, DISABLE);
            TIM_Cmd(TIM2, DISABLE);
            break;
        default:
            break;
    }

    // 切换到目标通道
    g_dds_handle.current_channel = target_channel;

    // 更新波形表指针
    switch (target_channel) {
        case DDS_CHANNEL_PRECISION:
            g_dds_handle.current_wave_table = (uint16_t*)g_sin_wave_table_precision;
            g_dds_handle.wave_table_size = DDS_WAVE_TABLE_SIZE_PRECISION;
            break;
        case DDS_CHANNEL_HIGH:
            g_dds_handle.current_wave_table = (uint16_t*)g_sin_wave_table_high;
            g_dds_handle.wave_table_size = DDS_WAVE_TABLE_SIZE_HIGH;
            break;
        case DDS_CHANNEL_FAST:
            g_dds_handle.current_wave_table = (uint16_t*)g_sin_wave_table_fast;
            g_dds_handle.wave_table_size = DDS_WAVE_TABLE_SIZE_FAST;
            g_dds_handle.wave_table_fast = (uint16_t*)g_sin_wave_table_fast;
            break;
        default:
            break;
    }

    // 启动新通道
    DDS_SyncPhase_CrossChannel();

    // 清除切换标志
    g_dds_handle.channel_switching = false;
}

/**
  * @brief  跨通道相位同步
  * @param  None
  * @retval None
  */
static void DDS_SyncPhase_CrossChannel(void)
{
    if (!g_dds_handle.config.enable_phase_continuity) {
        return;
    }

    // 根据波形表大小调整相位
    uint32_t old_table_bits = 12;  // 假设之前是4096点表
    uint32_t new_table_bits = DDS_GET_TABLE_BITS(g_dds_handle.wave_table_size);

    // 相位转换公式 (32位兼容模式)
    uint32_t new_phase = g_dds_handle.channel_switch_phase << (new_table_bits - old_table_bits);

    g_dds_handle.phase_accumulator = new_phase;

    // 同时更新32位相位累加器 (用于快速模式)
    g_dds_handle.phase_acc_fast = new_phase;
}

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  DDS初始化 (三段式架构)
  * @param  config: 配置参数指针
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_Init(DDS_Config_t* config)
{
    if (config == NULL) {
        return -1;
    }

    // 参数检查
    if (config->frequency < DDS_MIN_FREQUENCY || config->frequency > DDS_MAX_FREQUENCY) {
        return -1;
    }

    // 初始化句柄 - 三段式架构
    g_dds_handle.dac_internal = DAC;
    g_dds_handle.tim_precision = TIM7;   // 精密段定时器
    g_dds_handle.tim_high = TIM6;        // 高精度段定时器 (保持现有)
    g_dds_handle.tim_fast = TIM2;        // 极速段定时器
    g_dds_handle.dma_fast = DMA1_Stream6; // 极速段DMA

    g_dds_handle.config = *config;
    g_dds_handle.is_running = false;
    g_dds_handle.wave_updated = false;
    g_dds_handle.channel_switching = false;
    g_dds_handle.phase_sync_required = false;

    // 初始化32位DDS参数 (兼容模式)
    g_dds_handle.phase_accumulator = 0;
    g_dds_handle.frequency_word = 0;
    g_dds_handle.phase_acc_fast = 0;
    g_dds_handle.freq_word_fast = 0;
    g_dds_handle.mod_phase_acc = 0;
    g_dds_handle.channel_switch_phase = 0;

    // 初始化三段式波形表指针
    g_dds_handle.wave_table_precision = (uint16_t*)g_sin_wave_table_precision;
    g_dds_handle.wave_table_high = (uint16_t*)g_sin_wave_table_high;
    g_dds_handle.wave_table_fast = (uint16_t*)g_sin_wave_table_fast;

    // 自动选择初始通道
    if (config->channel == DDS_CHANNEL_AUTO) {
        g_dds_handle.current_channel = DDS_AUTO_SELECT_CHANNEL(config->frequency);
    } else {
        g_dds_handle.current_channel = config->channel;
    }
    g_dds_handle.target_channel = g_dds_handle.current_channel;

    // 设置默认调制参数
    memset(&g_dds_handle.modulation, 0, sizeof(DDS_Modulation_t));

    // 配置硬件 - 三段式
    DDS_GPIO_Config();              // 传统DAC GPIO (保持兼容)
    DDS_Internal_DAC_Config();      // 内置DAC1配置
    DDS_DAC_Config();               // 传统DAC配置 (保持兼容)

    // 配置定时器
    DDS_TIM_Config(DDS_SAMPLE_RATE_HIGH);        // 高精度段定时器
    DDS_TIM_Fast_Config(DDS_SAMPLE_RATE_FAST);   // 极速段定时器

    // 配置DMA
    DDS_DMA_Config();               // 传统DMA (保持兼容)
    DDS_Internal_DMA_Config();      // 内置DAC DMA
    DDS_NVIC_Config();

    // 生成波形表
    DDS_GenerateWaveTable(config->wave_type);

    // 计算频率控制字 (32位兼容模式)
    DDS_UpdateFrequencyWord();

    // 计算调制频率控制字
    g_dds_handle.mod_freq_word = DDS_CALC_FREQ_WORD_32(
        g_dds_handle.modulation.mod_frequency,
        DDS_SAMPLE_RATE_HIGH
    );

    // 预填充输出缓冲区
    for (uint32_t i = 0; i < DDS_OUTPUT_BUFFER_SIZE; i++) {
        s_dds_output_buffer[i] = DDS_GetNextSample();
    }

    // 预填充极速段缓冲区
    for (uint32_t buf = 0; buf < DDS_BUFFER_COUNT; buf++) {
        for (uint32_t i = 0; i < DDS_BUFFER_SIZE_FAST; i++) {
            s_dds_output_buffer_fast[buf][i] = DDS_GetNextSample_Fast();
        }
    }

    // 重置统计信息
    DDS_ResetStats();

    return 0;
}

// DDS_Start函数已移动到文件末尾，支持三段式架构

/**
  * @brief  停止DDS输出
  * @param  None
  * @retval None
  */
void DDS_Stop(void)
{
    // 停止定时器
    TIM_Cmd(DDS_TIM, DISABLE);

    // 停止DMA
    DMA_Cmd(DDS_DMA_STREAM, DISABLE);

    // 设置DAC输出为0
    DAC_SetChannel1Data(DAC_Align_12b_R, 0);

    g_dds_handle.is_running = false;
}

/**
  * @brief  设置输出频率 (三段式架构，自动通道切换)
  * @param  frequency: 频率(Hz)
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetFrequency(uint32_t frequency)
{
    if (frequency < DDS_MIN_FREQUENCY || frequency > DDS_MAX_FREQUENCY) {
        return -1;
    }

    // 检查是否需要切换通道
    int8_t channel_result = DDS_SelectOptimalChannel(frequency);
    if (channel_result != 0) {
        return -1;  // 通道切换失败或频率超出当前通道范围
    }

    g_dds_handle.config.frequency = frequency;

    // 使用32位兼容频率控制字
    DDS_UpdateFrequencyWord();

    // 更新统计信息
    g_dds_handle.stats.frequency_changes++;

    return 0;
}

/**
  * @brief  设置高精度频率 (支持小数)
  * @param  frequency_hz: 频率(Hz，支持小数)
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetFrequencyFloat(float frequency_hz)
{
    if (frequency_hz < (float)DDS_MIN_FREQUENCY || frequency_hz > (float)DDS_MAX_FREQUENCY) {
        return -1;
    }

    // 转换为整数频率进行通道选择
    uint32_t freq_int = (uint32_t)frequency_hz;
    int8_t channel_result = DDS_SelectOptimalChannel(freq_int);
    if (channel_result != 0) {
        return -1;
    }

    // 使用浮点数直接计算64位频率控制字 (超高精度)
    uint32_t sample_rate;
    switch (g_dds_handle.current_channel) {
        case DDS_CHANNEL_PRECISION:
            sample_rate = DDS_SAMPLE_RATE_PRECISION;
            break;
        case DDS_CHANNEL_HIGH:
            sample_rate = DDS_SAMPLE_RATE_HIGH;
            break;
        case DDS_CHANNEL_FAST:
            sample_rate = DDS_SAMPLE_RATE_FAST;
            break;
        default:
            sample_rate = DDS_SAMPLE_RATE_HIGH;
            break;
    }

    // 高精度浮点计算 (32位兼容模式)
    double freq_word_double = (frequency_hz * (1ULL << DDS_PHASE_BITS)) / (double)sample_rate;
    g_dds_handle.frequency_word = (uint32_t)freq_word_double;

    // 同时更新32位快速模式 (用于极速段)
    if (g_dds_handle.current_channel == DDS_CHANNEL_FAST) {
        g_dds_handle.freq_word_fast = g_dds_handle.frequency_word;
    }

    g_dds_handle.config.frequency = freq_int;  // 保存整数部分
    g_dds_handle.stats.frequency_changes++;

    return 0;
}

/**
  * @brief  设置波形类型
  * @param  wave_type: 波形类型
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetWaveType(DDS_WaveType_t wave_type)
{
    if (wave_type >= DDS_WAVE_COUNT) {
        return -1;
    }

    g_dds_handle.config.wave_type = wave_type;
    DDS_GenerateWaveTable(wave_type);
    g_dds_handle.wave_updated = true;

    // 更新统计信息
    g_dds_handle.stats.wave_changes++;

    return 0;
}

/**
  * @brief  设置幅度
  * @param  amplitude: 幅度(0-4095)
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetAmplitude(uint16_t amplitude)
{
    if (amplitude > DDS_AMPLITUDE_MAX) {
        return -1;
    }

    g_dds_handle.config.amplitude = amplitude;

    // 更新统计信息
    if (amplitude > g_dds_handle.stats.max_amplitude) {
        g_dds_handle.stats.max_amplitude = amplitude;
    }
    if (amplitude < g_dds_handle.stats.min_amplitude) {
        g_dds_handle.stats.min_amplitude = amplitude;
    }

    return 0;
}

/**
  * @brief  设置直流偏移
  * @param  offset: 偏移(0-4095)
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetOffset(uint16_t offset)
{
    if (offset > DDS_OFFSET_MAX) {
        return -1;
    }

    g_dds_handle.config.offset = offset;

    return 0;
}

/**
  * @brief  设置相位偏移
  * @param  phase: 相位(0-3599, 对应0-359.9度)
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetPhase(uint16_t phase)
{
    if (phase >= DDS_PHASE_RESOLUTION) {
        return -1;
    }

    g_dds_handle.config.phase = phase;

    // 将相位转换为相位累加器偏移 (32位兼容模式)
    uint32_t phase_offset = (uint32_t)((uint64_t)phase * (1ULL << DDS_PHASE_BITS) / DDS_PHASE_RESOLUTION);
    g_dds_handle.phase_accumulator = phase_offset;

    return 0;
}

/**
  * @brief  设置自定义波形
  * @param  wave_table: 波形表指针
  * @param  table_size: 表大小
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetCustomWave(const uint16_t* wave_table, uint32_t table_size)
{
    if (wave_table == NULL || table_size == 0 || table_size > DDS_WAVE_TABLE_SIZE) {
        return -1;
    }

    g_dds_handle.config.wave_type = DDS_WAVE_CUSTOM;
    g_dds_handle.current_wave_table = (uint16_t*)wave_table;
    g_dds_handle.wave_table_size = table_size;
    g_dds_handle.wave_updated = true;

    return 0;
}

/**
  * @brief  配置调制
  * @param  modulation: 调制参数指针
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_ConfigModulation(DDS_Modulation_t* modulation)
{
    if (modulation == NULL) {
        return -1;
    }

    g_dds_handle.modulation = *modulation;

    // 计算调制频率控制字
    g_dds_handle.mod_freq_word = DDS_CALC_FREQ_WORD(
        modulation->mod_frequency,
        g_dds_handle.config.sample_rate
    );

    return 0;
}

/**
  * @brief  使能/禁用调制
  * @param  enable: true-使能, false-禁用
  * @retval None
  */
void DDS_EnableModulation(bool enable)
{
    g_dds_handle.config.enable_modulation = enable;
}

/**
  * @brief  获取当前输出值
  * @param  None
  * @retval 当前DAC输出值
  */
uint16_t DDS_GetCurrentOutput(void)
{
    return DAC_GetDataOutputValue(DDS_DAC_CHANNEL);
}

/**
  * @brief  获取统计信息
  * @param  stats: 统计信息结构体指针
  * @retval None
  */
void DDS_GetStats(DDS_Stats_t* stats)
{
    if (stats == NULL) {
        return;
    }

    *stats = g_dds_handle.stats;

    // 计算实际频率 (32位兼容模式)
    if (g_dds_handle.config.sample_rate > 0) {
        stats->actual_frequency = (float)g_dds_handle.frequency_word *
                                 g_dds_handle.config.sample_rate / (1UL << DDS_PHASE_BITS);

        // 计算频率误差
        if (g_dds_handle.config.frequency > 0) {
            stats->frequency_error = (stats->actual_frequency - g_dds_handle.config.frequency) *
                                    100.0f / g_dds_handle.config.frequency;
        }
    }
}

/**
  * @brief  重置统计信息
  * @param  None
  * @retval None
  */
void DDS_ResetStats(void)
{
    memset(&g_dds_handle.stats, 0, sizeof(DDS_Stats_t));
    g_dds_handle.stats.min_amplitude = 0xFFFF;
}

/**
  * @brief  计算THD(总谐波失真)
  * @param  None
  * @retval THD百分比
  */
float DDS_CalculateTHD(void)
{
    // 简化的THD计算
    // 实际应用中需要进行FFT分析
    return g_dds_handle.stats.thd_percent;
}

/**
  * @brief  设置输出通道
  * @param  channel: 输出通道
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SetChannel(DDS_Channel_t channel)
{
    if (channel >= DDS_CHANNEL_AUTO) {
        return -1;
    }

    // 检查频率是否在目标通道范围内
    switch (channel) {
        case DDS_CHANNEL_PRECISION:
            if (g_dds_handle.config.frequency > DDS_FREQ_THRESHOLD_PRECISION) {
                return -1;
            }
            break;
        case DDS_CHANNEL_HIGH:
            if (g_dds_handle.config.frequency > DDS_FREQ_THRESHOLD_HIGH) {
                return -1;
            }
            break;
        case DDS_CHANNEL_FAST:
            if (g_dds_handle.config.frequency > DDS_MAX_FREQUENCY) {
                return -1;
            }
            break;
        default:
            return -1;
    }

    DDS_SwitchChannel_Internal(channel);
    return 0;
}

/**
  * @brief  获取当前通道
  * @param  None
  * @retval 当前输出通道
  */
DDS_Channel_t DDS_GetCurrentChannel(void)
{
    return g_dds_handle.current_channel;
}

/**
  * @brief  强制通道切换 (带相位同步)
  * @param  target_channel: 目标通道
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_SwitchChannel(DDS_Channel_t target_channel)
{
    if (target_channel >= DDS_CHANNEL_AUTO) {
        return -1;
    }

    // 临时启用相位连续性
    bool old_phase_continuity = g_dds_handle.config.enable_phase_continuity;
    g_dds_handle.config.enable_phase_continuity = true;

    DDS_SwitchChannel_Internal(target_channel);

    // 恢复原设置
    g_dds_handle.config.enable_phase_continuity = old_phase_continuity;

    return 0;
}

/**
  * @brief  获取实际输出频率
  * @param  None
  * @retval 实际频率(Hz)
  */
float DDS_GetActualFrequency(void)
{
    uint32_t sample_rate;
    switch (g_dds_handle.current_channel) {
        case DDS_CHANNEL_PRECISION:
            sample_rate = DDS_SAMPLE_RATE_PRECISION;
            break;
        case DDS_CHANNEL_HIGH:
            sample_rate = DDS_SAMPLE_RATE_HIGH;
            break;
        case DDS_CHANNEL_FAST:
            sample_rate = DDS_SAMPLE_RATE_FAST;
            break;
        default:
            sample_rate = DDS_SAMPLE_RATE_HIGH;
            break;
    }

    // 使用32位精度计算实际频率
    double actual_freq = ((double)g_dds_handle.frequency_word * sample_rate) / (1UL << DDS_PHASE_BITS);
    return (float)actual_freq;
}

/**
  * @brief  开始DDS输出 (更新实现)
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t DDS_Start(void)
{
    if (g_dds_handle.is_running) {
        return -1; // 已经在运行
    }

    // 清除标志
    g_dds_update_complete = false;
    g_dds_dma_complete = false;

    // 根据当前通道启动相应的硬件
    switch (g_dds_handle.current_channel) {
        case DDS_CHANNEL_PRECISION:
        case DDS_CHANNEL_HIGH:
            // 启动传统DMA (保持现有实现)
            DMA_Cmd(DDS_DMA_STREAM, ENABLE);
            TIM_Cmd(DDS_TIM, ENABLE);
            break;

        case DDS_CHANNEL_FAST:
            // 启动极速段 - 内置DAC + 高速DMA
            DMA_Cmd(g_dds_handle.dma_fast, ENABLE);
            TIM_Cmd(g_dds_handle.tim_fast, ENABLE);
            break;

        default:
            return -1;
    }

    g_dds_handle.is_running = true;
    return 0;
}

/**
  * @brief  DDS内部TIM6中断处理函数
  * @param  None
  * @retval None
  */
void DDS_TIM6_IRQHandler_Internal(void)
{
    if (TIM_GetITStatus(DDS_TIM, TIM_IT_Update) != RESET) {
        TIM_ClearITPendingBit(DDS_TIM, TIM_IT_Update);

        // 调用G题专用DDS更新函数 - 精确的硬件定时器触发
        extern void G_DDS_UpdateOutput(void);
        G_DDS_UpdateOutput();

        // 设置更新完成标志
        g_dds_update_complete = true;
    }
}

/**
  * @brief  DMA1_Stream5中断处理函数
  * @param  None
  * @retval None
  */
void DMA1_Stream5_IRQHandler(void)
{
    // 检查半传输完成中断
    if (DMA_GetITStatus(DDS_DMA_STREAM, DMA_IT_HTIF5) != RESET) {
        DMA_ClearITPendingBit(DDS_DMA_STREAM, DMA_IT_HTIF5);

        // 更新前半部分缓冲区
        for (uint32_t i = 0; i < DDS_OUTPUT_BUFFER_SIZE / 2; i++) {
            s_dds_output_buffer[i] = DDS_GetNextSample();
        }
    }

    // 检查传输完成中断
    if (DMA_GetITStatus(DDS_DMA_STREAM, DMA_IT_TCIF5) != RESET) {
        DMA_ClearITPendingBit(DDS_DMA_STREAM, DMA_IT_TCIF5);

        // 更新后半部分缓冲区
        for (uint32_t i = DDS_OUTPUT_BUFFER_SIZE / 2; i < DDS_OUTPUT_BUFFER_SIZE; i++) {
            s_dds_output_buffer[i] = DDS_GetNextSample();
        }

        g_dds_dma_complete = true;
    }

    // 检查传输错误中断
    if (DMA_GetITStatus(DDS_DMA_STREAM, DMA_IT_TEIF5) != RESET) {
        DMA_ClearITPendingBit(DDS_DMA_STREAM, DMA_IT_TEIF5);
        // 处理传输错误
    }
}

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
